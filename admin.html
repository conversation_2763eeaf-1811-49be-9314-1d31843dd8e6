<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة AL-SALAMAT</title>
    <link rel="stylesheet" href="admin-styles.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-storage-compat.js"></script>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <p>جاري التحميل...</p>
    </div>

    <!-- Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <h1>🛠️ لوحة إدارة AL-SALAMAT</h1>
            <div class="admin-header-actions">
                <div class="connection-status" id="admin-connection-status">
                    <span class="status-indicator online"></span>
                    <span>متصل</span>
                </div>
                <button class="admin-btn secondary" onclick="previewSite()">👁️ معاينة الموقع</button>
                <button class="admin-btn danger" onclick="logout()">🚪 تسجيل الخروج</button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="admin-sidebar">
            <div class="admin-menu">
                <button class="menu-item active" onclick="showSection('company-info')" data-section="company-info">
                    🏢 معلومات الشركة
                </button>
                <button class="menu-item" onclick="showSection('logo')" data-section="logo">
                    🖼️ إدارة اللوجو
                </button>
                <button class="menu-item" onclick="showSection('about')" data-section="about">
                    📝 من نحن
                </button>
                <button class="menu-item" onclick="showSection('banner')" data-section="banner">
                    📢 الشريط المتحرك
                </button>
                <button class="menu-item" onclick="showSection('hero-images')" data-section="hero-images">
                    🚗 صور القائمة الأولى
                </button>
                <button class="menu-item" onclick="showSection('service-images')" data-section="service-images">
                    🔧 صور الخدمات
                </button>
                <button class="menu-item" onclick="showSection('gallery')" data-section="gallery">
                    🖼️ معرض الصور
                </button>
                <button class="menu-item" onclick="showSection('branches')" data-section="branches">
                    🏪 إدارة الفروع
                </button>
                <button class="menu-item" onclick="showSection('contact')" data-section="contact">
                    📞 معلومات التواصل
                </button>
                <button class="menu-item" onclick="showSection('translations')" data-section="translations">
                    🌐 إدارة النصوص والترجمة
                </button>
                <button class="menu-item" onclick="showSection('settings')" data-section="settings">
                    ⚙️ إعدادات الموقع
                </button>
                <button class="menu-item" onclick="showSection('users')" data-section="users">
                    👥 إدارة المستخدمين
                </button>
                <button class="menu-item" onclick="showSection('messages')" data-section="messages">
                    📧 الرسائل الواردة
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Success/Error Messages -->
            <div class="admin-message" id="admin-message"></div>

            <!-- Company Info Section -->
            <section class="admin-section active" id="company-info">
                <div class="section-header">
                    <h2>🏢 معلومات الشركة</h2>
                    <p>تحكم في العنوان الرئيسي والفرعي ووصف الشركة باللغتين العربية والإنجليزية</p>
                </div>

                <div class="admin-card">
                    <form id="company-info-form" class="admin-form">
                        <div class="translation-group">
                            <h4>العنوان الرئيسي</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="company-title-ar">العربية</label>
                                    <input type="text" id="company-title-ar" name="titleAr" placeholder="السلامات لزجاج السيارات" required>
                                </div>
                                <div class="form-group">
                                    <label for="company-title-en">English</label>
                                    <input type="text" id="company-title-en" name="titleEn" placeholder="AL-SALAMAT Car Glass" required>
                                </div>
                            </div>
                        </div>

                        <div class="translation-group">
                            <h4>العنوان الفرعي</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="company-subtitle-ar">العربية</label>
                                    <input type="text" id="company-subtitle-ar" name="subtitleAr" placeholder="رائدة في زجاج السيارات" required>
                                </div>
                                <div class="form-group">
                                    <label for="company-subtitle-en">English</label>
                                    <input type="text" id="company-subtitle-en" name="subtitleEn" placeholder="Leading in Car Glass Services" required>
                                </div>
                            </div>
                        </div>

                        <div class="translation-group">
                            <h4>وصف الشركة</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="company-description-ar">العربية</label>
                                    <textarea id="company-description-ar" name="descriptionAr" rows="4" placeholder="وصف مختصر عن الشركة وخدماتها باللغة العربية"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="company-description-en">English</label>
                                    <textarea id="company-description-en" name="descriptionEn" rows="4" placeholder="Brief description about the company and its services in English"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadCompanyInfo()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Logo Management Section -->
            <section class="admin-section" id="logo">
                <div class="section-header">
                    <h2>🖼️ إدارة اللوجو</h2>
                    <p>تحكم في صورة اللوجو التي تظهر بجانب اسم الشركة</p>
                </div>

                <div class="admin-card">
                    <h3>🖼️ اللوجو الحالي</h3>
                    <div class="logo-management-container">
                        <div class="logo-preview-section">
                            <div class="logo-preview-container">
                                <img src="" alt="لوجو الشركة" id="current-logo-preview" class="logo-preview-image">
                                <div class="logo-placeholder" id="logo-placeholder">
                                    <div class="placeholder-icon">🖼️</div>
                                    <p>لا يوجد لوجو حالياً</p>
                                </div>
                            </div>
                            <div class="logo-info">
                                <h4>معاينة اللوجو</h4>
                                <p>هذا هو شكل اللوجو كما سيظهر في الموقع</p>
                            </div>
                        </div>

                        <div class="logo-upload-section">
                            <h4>📤 رفع لوجو جديد</h4>
                            <form id="logo-upload-form" class="admin-form">
                                <div class="form-group">
                                    <label for="logo-file">اختر صورة اللوجو</label>
                                    <input type="file" id="logo-file" accept="image/*" required>
                                    <small>الصيغ المدعومة: PNG, JPG, SVG (يُفضل PNG بخلفية شفافة)</small>
                                </div>

                                <div class="form-group">
                                    <label for="logo-alt">نص بديل للصورة</label>
                                    <input type="text" id="logo-alt" placeholder="لوجو شركة السلامات" value="لوجو شركة السلامات">
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="logo-enabled" checked>
                                        <span class="checkmark"></span>
                                        عرض اللوجو في الموقع
                                    </label>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="admin-btn primary">💾 حفظ اللوجو</button>
                                    <button type="button" class="admin-btn secondary" onclick="previewLogo()">👁️ معاينة</button>
                                    <button type="button" class="admin-btn info" onclick="useDefaultLogo()">🏠 استخدام اللوجو الافتراضي</button>
                                    <button type="button" class="admin-btn danger" onclick="removeLogo()">🗑️ حذف اللوجو</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="logo-settings-info">
                        <h4>📋 معلومات مهمة:</h4>
                        <ul>
                            <li>الصيغ المدعومة: PNG, JPG, SVG</li>
                            <li>الحد الأقصى لحجم الملف: 2MB</li>
                            <li>الأبعاد المفضلة: 64x64 بكسل أو أصغر</li>
                            <li>يُفضل استخدام PNG بخلفية شفافة</li>
                            <li>سيظهر اللوجو في أعلى اليمين من الصفحة الرئيسية</li>
                            <li>يمكن إيقاف عرض اللوجو مؤقتاً دون حذفه</li>
                            <li><strong>اللوجو الافتراضي:</strong> يتم استخدام logo2.png كلوجو افتراضي</li>
                            <li><strong>زر "استخدام اللوجو الافتراضي":</strong> يعيد تعيين اللوجو إلى الصورة الافتراضية</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- About Section -->
            <section class="admin-section" id="about">
                <div class="section-header">
                    <h2>📝 من نحن</h2>
                    <p>تحكم في محتوى قسم "من نحن" في الصفحة الرئيسية باللغتين العربية والإنجليزية</p>
                </div>

                <div class="admin-card">
                    <form id="about-form" class="admin-form">
                        <div class="translation-group">
                            <h4>عنوان القسم</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="about-title-ar">العربية</label>
                                    <input type="text" id="about-title-ar" name="titleAr" placeholder="من نحن" required>
                                </div>
                                <div class="form-group">
                                    <label for="about-title-en">English</label>
                                    <input type="text" id="about-title-en" name="titleEn" placeholder="About Us" required>
                                </div>
                            </div>
                        </div>

                        <div class="translation-group">
                            <h4>وصف الشركة</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="about-description-ar">العربية</label>
                                    <textarea id="about-description-ar" name="descriptionAr" rows="6" placeholder="اكتب وصفاً مفصلاً عن الشركة وخدماتها ورؤيتها..." required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="about-description-en">English</label>
                                    <textarea id="about-description-en" name="descriptionEn" rows="6" placeholder="Write a detailed description about the company, its services and vision..." required></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadAboutInfo()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Moving Banner Section -->
            <section class="admin-section" id="banner">
                <div class="section-header">
                    <h2>📢 إدارة الشريط المتحرك</h2>
                    <p>تحكم في نص وإعدادات الشريط المتحرك باللغتين العربية والإنجليزية</p>
                </div>

                <div class="admin-card">
                    <h3>⚙️ إعدادات الشريط</h3>
                    <form id="banner-form" class="admin-form">
                        <div class="translation-group">
                            <h4>نص الشريط المتحرك</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="banner-text-ar">العربية</label>
                                    <textarea id="banner-text-ar" name="bannerTextAr" rows="3" placeholder="أدخل النص المراد عرضه في الشريط المتحرك باللغة العربية" required>السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات</textarea>
                                </div>
                                <div class="form-group">
                                    <label for="banner-text-en">English</label>
                                    <textarea id="banner-text-en" name="bannerTextEn" rows="3" placeholder="Enter the text to be displayed in the moving banner in English" required>AL-SALAMAT Car Glass - Specialists in car glass replacement and installation</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="banner-enabled" name="bannerEnabled" checked>
                                <span class="checkmark"></span>
                                تفعيل الشريط المتحرك
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                            <button type="button" class="admin-btn secondary" onclick="previewBanner()">👁️ معاينة</button>
                            <button type="button" class="admin-btn secondary" onclick="loadBannerSettings()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Hero Images Section -->
            <section class="admin-section" id="hero-images">
                <div class="section-header">
                    <h2>🚗 إدارة صور القائمة الأولى</h2>
                    <p>تحديث الصورة الرئيسية للشركة (main1)</p>
                </div>

                <div class="admin-card">
                    <h3>🖼️ الصور الحالية</h3>
                    <div class="hero-images-grid" id="hero-images-grid">
                        <!-- Company Main Image -->
                        <div class="hero-image-item" data-image="main1">
                            <div class="hero-image-preview">
                                <img src="img/main1.jpg" alt="صورة الشركة الرئيسية" id="preview-main1">
                                <div class="hero-image-overlay">
                                    <h4>صورة الشركة الرئيسية</h4>
                                    <p>main1.jpg</p>
                                </div>
                            </div>
                            <div class="hero-image-actions">
                                <input type="file" id="file-main1" accept="image/*" style="display: none;" onchange="previewHeroImage('main1', this)">
                                <button class="admin-btn primary" onclick="document.getElementById('file-main1').click()">
                                    📤 تغيير الصورة
                                </button>
                                <button class="admin-btn success" onclick="uploadHeroImage('main1')" id="upload-main1" style="display: none;">
                                    💾 حفظ
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="hero-images-info">
                        <h4>📋 معلومات مهمة:</h4>
                        <ul>
                            <li>الصيغ المدعومة: JPG, PNG, GIF</li>
                            <li>الحد الأقصى لحجم الملف: 5MB</li>
                            <li>الأبعاد المفضلة: 800x600 بكسل أو أكبر</li>
                            <li>ستظهر التغييرات فوراً في الموقع</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Service Images Section -->
            <section class="admin-section" id="service-images">
                <div class="section-header">
                    <h2>🔧 إدارة صور الخدمات</h2>
                    <p>تحديث صور الخدمات في القسم الأول (خدمات زجاج السيارات، تركيب زجاج السيارات، إصلاح زجاج السيارات)</p>
                </div>

                <div class="admin-card">
                    <h3>🖼️ صور الخدمات الحالية</h3>
                    <div class="service-images-grid" id="service-images-grid">
                        <!-- Service 1: خدمات زجاج السيارات -->
                        <div class="service-image-item" data-service="car2">
                            <div class="service-image-preview">
                                <img src="img/car2.png" alt="خدمات زجاج السيارات" id="preview-car2-service">
                                <div class="service-image-overlay">
                                    <h4 id="title-car2-service">خدمات زجاج السيارات</h4>
                                    <p id="desc-car2-service">أفضل خدمات تركيب وإصلاح زجاج السيارات</p>
                                    <span class="image-filename">car2.png</span>
                                </div>
                            </div>
                            <div class="service-image-form">
                                <div class="form-group">
                                    <label for="title-input-car2">عنوان الخدمة (اختياري)</label>
                                    <input type="text" id="title-input-car2" value="خدمات زجاج السيارات" placeholder="اتركه فارغاً لاستخدام العنوان الافتراضي">
                                </div>
                                <div class="form-group">
                                    <label for="desc-input-car2">وصف الخدمة</label>
                                    <textarea id="desc-input-car2" rows="2" placeholder="أدخل وصف الخدمة">أفضل خدمات تركيب وإصلاح زجاج السيارات</textarea>
                                </div>
                            </div>
                            <div class="service-image-actions">
                                <input type="file" id="file-car2-service" accept="image/*" style="display: none;" onchange="previewServiceImage('car2', this)">
                                <button class="admin-btn primary" onclick="document.getElementById('file-car2-service').click()">
                                    📤 تغيير الصورة
                                </button>
                                <button class="admin-btn secondary" onclick="updateServiceText('car2')">
                                    ✏️ تحديث النص
                                </button>
                                <button class="admin-btn success" onclick="uploadServiceImage('car2')" id="upload-car2-service" style="display: none;">
                                    💾 حفظ الصورة
                                </button>
                            </div>
                        </div>

                        <!-- Service 2: تركيب زجاج السيارات -->
                        <div class="service-image-item" data-service="car4">
                            <div class="service-image-preview">
                                <img src="img/car4.png" alt="تركيب زجاج السيارات" id="preview-car4-service">
                                <div class="service-image-overlay">
                                    <h4 id="title-car4-service">تركيب زجاج السيارات</h4>
                                    <p id="desc-car4-service">تركيب احترافي بأعلى معايير الجودة</p>
                                    <span class="image-filename">car4.png</span>
                                </div>
                            </div>
                            <div class="service-image-form">
                                <div class="form-group">
                                    <label for="title-input-car4">عنوان الخدمة (اختياري)</label>
                                    <input type="text" id="title-input-car4" value="تركيب زجاج السيارات" placeholder="اتركه فارغاً لاستخدام العنوان الافتراضي">
                                </div>
                                <div class="form-group">
                                    <label for="desc-input-car4">وصف الخدمة</label>
                                    <textarea id="desc-input-car4" rows="2" placeholder="أدخل وصف الخدمة">تركيب احترافي بأعلى معايير الجودة</textarea>
                                </div>
                            </div>
                            <div class="service-image-actions">
                                <input type="file" id="file-car4-service" accept="image/*" style="display: none;" onchange="previewServiceImage('car4', this)">
                                <button class="admin-btn primary" onclick="document.getElementById('file-car4-service').click()">
                                    📤 تغيير الصورة
                                </button>
                                <button class="admin-btn secondary" onclick="updateServiceText('car4')">
                                    ✏️ تحديث النص
                                </button>
                                <button class="admin-btn success" onclick="uploadServiceImage('car4')" id="upload-car4-service" style="display: none;">
                                    💾 حفظ الصورة
                                </button>
                            </div>
                        </div>

                        <!-- Service 3: إصلاح زجاج السيارات -->
                        <div class="service-image-item" data-service="car6">
                            <div class="service-image-preview">
                                <img src="img/car6.png" alt="إصلاح زجاج السيارات" id="preview-car6-service">
                                <div class="service-image-overlay">
                                    <h4 id="title-car6-service">إصلاح زجاج السيارات</h4>
                                    <p id="desc-car6-service">إصلاح سريع وفعال لجميع أنواع السيارات</p>
                                    <span class="image-filename">car6.png</span>
                                </div>
                            </div>
                            <div class="service-image-form">
                                <div class="form-group">
                                    <label for="title-input-car6">عنوان الخدمة (اختياري)</label>
                                    <input type="text" id="title-input-car6" value="إصلاح زجاج السيارات" placeholder="اتركه فارغاً لاستخدام العنوان الافتراضي">
                                </div>
                                <div class="form-group">
                                    <label for="desc-input-car6">وصف الخدمة</label>
                                    <textarea id="desc-input-car6" rows="2" placeholder="أدخل وصف الخدمة">إصلاح سريع وفعال لجميع أنواع السيارات</textarea>
                                </div>
                            </div>
                            <div class="service-image-actions">
                                <input type="file" id="file-car6-service" accept="image/*" style="display: none;" onchange="previewServiceImage('car6', this)">
                                <button class="admin-btn primary" onclick="document.getElementById('file-car6-service').click()">
                                    📤 تغيير الصورة
                                </button>
                                <button class="admin-btn secondary" onclick="updateServiceText('car6')">
                                    ✏️ تحديث النص
                                </button>
                                <button class="admin-btn success" onclick="uploadServiceImage('car6')" id="upload-car6-service" style="display: none;">
                                    💾 حفظ الصورة
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="service-images-info">
                        <h4>📋 معلومات مهمة:</h4>
                        <ul>
                            <li>الصيغ المدعومة: JPG, PNG, GIF</li>
                            <li>الحد الأقصى لحجم الملف: 5MB</li>
                            <li>الأبعاد المفضلة: 600x400 بكسل أو أكبر</li>
                            <li>ستظهر التغييرات فوراً في الصفحة الرئيسية</li>
                            <li>هذه الصور تظهر في قسم الخدمات في أعلى الصفحة</li>
                            <li><strong>عنوان الخدمة اختياري:</strong> إذا تركته فارغاً سيتم استخدام العنوان الافتراضي</li>
                            <li><strong>لحفظ النص فقط:</strong> استخدم زر "تحديث النص"</li>
                            <li><strong>لحفظ الصورة:</strong> اختر صورة جديدة ثم اضغط "حفظ الصورة"</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Gallery Section -->
            <section class="admin-section" id="gallery">
                <div class="section-header">
                    <h2>🖼️ إدارة معرض الصور</h2>
                    <p>إضافة وتعديل وحذف صور المعرض</p>
                </div>

                <div class="admin-card">
                    <h3>📤 رفع صورة جديدة</h3>
                    <form id="upload-image-form" class="admin-form" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="image-file">اختر الصورة</label>
                            <input type="file" id="image-file" name="imageFile" accept="image/*" required>
                            <small>الصيغ المدعومة: JPG, PNG, GIF (الحد الأقصى: 5MB)</small>
                        </div>

                        <div class="form-group">
                            <label for="image-title">عنوان الصورة</label>
                            <input type="text" id="image-title" name="imageTitle" placeholder="أدخل عنوان الصورة" required>
                        </div>

                        <div class="form-group">
                            <label for="image-description">وصف الصورة</label>
                            <textarea id="image-description" name="imageDescription" rows="3" placeholder="أدخل وصف الصورة" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="image-category">فئة الصورة</label>
                            <select id="image-category" name="imageCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="services">خدماتنا</option>
                                <option value="installation">التركيب</option>
                                <option value="repair">الإصلاح</option>
                                <option value="products">منتجاتنا</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="image-featured" name="imageFeatured">
                                <span class="checkmark"></span>
                                صورة مميزة (تظهر في المقدمة)
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">📤 رفع الصورة</button>
                            <button type="button" class="admin-btn secondary" onclick="clearImageForm()">🗑️ مسح النموذج</button>
                        </div>

                        <div id="upload-progress" class="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <span id="progress-text">0%</span>
                        </div>
                    </form>
                </div>

                <div class="admin-card">
                    <h3>🖼️ الصور الحالية</h3>
                    <div class="gallery-controls">
                        <div class="search-box">
                            <input type="text" id="gallery-search" placeholder="البحث في الصور..." onkeyup="filterGalleryImages()">
                            <button onclick="filterGalleryImages()">🔍</button>
                        </div>
                        <div class="filter-controls">
                            <select id="category-filter" onchange="filterGalleryImages()">
                                <option value="">جميع الفئات</option>
                                <option value="services">خدماتنا</option>
                                <option value="installation">التركيب</option>
                                <option value="repair">الإصلاح</option>
                                <option value="products">منتجاتنا</option>
                            </select>
                        </div>
                    </div>

                    <div id="gallery-images-grid" class="gallery-images-grid">
                        <!-- سيتم عرض الصور هنا -->
                    </div>

                    <div id="gallery-pagination" class="pagination">
                        <!-- أزرار التنقل -->
                    </div>
                </div>
            </section>

            <!-- Branches Section -->
            <section class="admin-section" id="branches">
                <div class="section-header">
                    <h2>🏪 إدارة الفروع</h2>
                    <p>إضافة وتعديل وحذف فروع الشركة</p>
                </div>
                
                <!-- Add New Branch -->
                <div class="admin-card">
                    <h3>➕ إضافة فرع جديد</h3>
                    <form id="add-branch-form" class="admin-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="branch-name">اسم الفرع</label>
                                <input type="text" id="branch-name" name="name" placeholder="فرع الرياض" required>
                            </div>
                            <div class="form-group">
                                <label for="branch-phone">رقم الهاتف</label>
                                <input type="tel" id="branch-phone" name="phone" placeholder="0100000000">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="branch-address">العنوان الكامل</label>
                            <textarea id="branch-address" name="address" rows="3" placeholder="العنوان التفصيلي للفرع" required></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">➕ إضافة الفرع</button>
                            <button type="reset" class="admin-btn secondary">🗑️ مسح النموذج</button>
                        </div>
                    </form>
                </div>
                
                <!-- Existing Branches -->
                <div class="admin-card">
                    <h3>📋 الفروع الحالية</h3>
                    <div class="branches-grid" id="branches-grid">
                        <div class="no-data" id="no-branches">
                            <p>لا توجد فروع مضافة حالياً</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Section -->
            <section class="admin-section" id="contact">
                <div class="section-header">
                    <h2>📞 معلومات التواصل</h2>
                    <p>تحديث معلومات التواصل والعنوان وساعات العمل</p>
                </div>
                
                <div class="admin-card">
                    <form id="contact-form" class="admin-form">
                        <div class="form-group">
                            <label for="contact-title">عنوان قسم التواصل</label>
                            <input type="text" id="contact-title" name="title" placeholder="اتصل بنا" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-info-title">عنوان معلومات التواصل</label>
                            <input type="text" id="contact-info-title" name="infoTitle" placeholder="معلومات التواصل" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-address">العنوان الفيزيائي</label>
                            <textarea id="contact-address" name="address" rows="3" placeholder="العنوان الكامل للشركة" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-hours">ساعات العمل</label>
                            <input type="text" id="contact-hours" name="hours" placeholder="######" required>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadContactInfo()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Settings Section -->
            <section class="admin-section" id="settings">
                <div class="section-header">
                    <h2>⚙️ إعدادات الموقع</h2>
                    <p>إعدادات عامة للموقع ومعلومات التواصل الأساسية</p>
                </div>
                
                <div class="admin-card">
                    <form id="settings-form" class="admin-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="site-email">البريد الإلكتروني</label>
                                <input type="email" id="site-email" name="contactEmail" placeholder="######" required>
                            </div>
                            <div class="form-group">
                                <label for="site-phone">رقم الهاتف</label>
                                <input type="tel" id="site-phone" name="contactPhone" placeholder="######" required>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ الإعدادات</button>
                            <button type="button" class="admin-btn secondary" onclick="loadSettings()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Users Section -->
            <section class="admin-section" id="users">
                <div class="section-header">
                    <h2>👥 إدارة المستخدمين</h2>
                    <p>عرض وإدارة المستخدمين المسجلين</p>
                </div>
                
                <div class="admin-card">
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الدور</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="users-table">
                                <tr>
                                    <td colspan="6" class="no-data">جاري تحميل المستخدمين...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Translations Section -->
            <section class="admin-section" id="translations">
                <div class="section-header">
                    <h2>🌐 إدارة النصوص والترجمة</h2>
                    <p>تحكم في النصوص العربية والإنجليزية للموقع</p>
                </div>



                <div class="admin-card">
                    <h3>📋 نصوص القوائم</h3>
                    <form id="menu-texts-form" class="admin-form">
                        <div class="translation-group">
                            <h4>قائمة التنقل</h4>
                            <div class="menu-item-translation">
                                <label>الرئيسية</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="nav-home-ar" value="الرئيسية" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="nav-home-en" value="Home" required>
                                    </div>
                                </div>
                            </div>

                            <div class="menu-item-translation">
                                <label>من نحن</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="nav-about-ar" value="من نحن" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="nav-about-en" value="About Us" required>
                                    </div>
                                </div>
                            </div>

                            <div class="menu-item-translation">
                                <label>معرض الصور</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="nav-gallery-ar" value="معرض الصور" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="nav-gallery-en" value="Gallery" required>
                                    </div>
                                </div>
                            </div>

                            <div class="menu-item-translation">
                                <label>الفروع</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="nav-branches-ar" value="الفروع" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="nav-branches-en" value="Branches" required>
                                    </div>
                                </div>
                            </div>

                            <div class="menu-item-translation">
                                <label>اتصل بنا</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="nav-contact-ar" value="اتصل بنا" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="nav-contact-en" value="Contact Us" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ نصوص القوائم</button>
                            <button type="button" class="admin-btn secondary" onclick="loadMenuTexts()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>

                <div class="admin-card">
                    <h3>📞 نصوص قسم اتصل بنا</h3>
                    <form id="contact-texts-form" class="admin-form">
                        <div class="translation-group">
                            <h4>عنوان القسم</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-title-ar">العربية</label>
                                    <input type="text" id="contact-title-ar" value="اتصل بنا" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-title-en">English</label>
                                    <input type="text" id="contact-title-en" value="Contact Us" required>
                                </div>
                            </div>
                        </div>

                        <div class="translation-group">
                            <h4>معلومات التواصل</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-info-title-ar">العربية</label>
                                    <input type="text" id="contact-info-title-ar" value="معلومات التواصل" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-info-title-en">English</label>
                                    <input type="text" id="contact-info-title-en" value="Contact Information" required>
                                </div>
                            </div>
                        </div>

                        <div class="translation-group">
                            <h4>نموذج الاتصال</h4>
                            <div class="contact-form-translations">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>عنوان النموذج - العربية</label>
                                        <input type="text" id="contact-form-title-ar" value="أرسل لنا رسالة" required>
                                    </div>
                                    <div class="form-group">
                                        <label>عنوان النموذج - English</label>
                                        <input type="text" id="contact-form-title-en" value="Send us a Message" required>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>زر الإرسال - العربية</label>
                                        <input type="text" id="contact-submit-ar" value="إرسال الرسالة" required>
                                    </div>
                                    <div class="form-group">
                                        <label>زر الإرسال - English</label>
                                        <input type="text" id="contact-submit-en" value="Send Message" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ نصوص الاتصال</button>
                            <button type="button" class="admin-btn secondary" onclick="loadContactTexts()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>

                <div class="admin-card">
                    <h3>📋 نصوص أقسام الموقع</h3>
                    <form id="sections-texts-form" class="admin-form">
                        <div class="translation-group">
                            <h4>عناوين الأقسام الرئيسية</h4>

                            <div class="menu-item-translation">
                                <label>من نحن</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="about-section-ar" value="من نحن" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="about-section-en" value="About Us" required>
                                    </div>
                                </div>
                            </div>

                            <div class="menu-item-translation">
                                <label>معرض الصور</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="gallery-section-ar" value="معرض الصور" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="gallery-section-en" value="Photo Gallery" required>
                                    </div>
                                </div>
                            </div>

                            <div class="menu-item-translation">
                                <label>فروعنا</label>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="branches-section-ar" value="فروعنا" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="branches-section-en" value="Our Branches" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="admin-btn primary">💾 حفظ نصوص الأقسام</button>
                            <button type="button" class="admin-btn secondary" onclick="loadSectionsTexts()">🔄 إعادة تحميل</button>
                        </div>
                    </form>
                </div>



                <div class="admin-card">
                    <h3>ℹ️ معلومات مهمة</h3>
                    <ul>
                        <li><strong>التحديث الفوري:</strong> التغييرات تظهر فوراً في الموقع</li>
                        <li><strong>النصوص الافتراضية:</strong> يمكن العودة للنصوص الافتراضية في أي وقت</li>
                        <li><strong>التحقق:</strong> تأكد من صحة النصوص قبل الحفظ</li>
                        <li><strong>اللغة الافتراضية:</strong> العربية هي اللغة الافتراضية للموقع</li>
                        <li><strong>تبديل اللغة:</strong> يمكن للزوار تبديل اللغة من زر 🌐 في الهيدر</li>
                        <li><strong>نصوص الإدارة:</strong> تؤثر على عناوين أقسام لوحة الإدارة</li>
                        <li><strong>نصوص الأقسام:</strong> تؤثر على عناوين أقسام الموقع الرئيسي</li>
                    </ul>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="admin-section" id="messages">
                <div class="section-header">
                    <h2>📧 الرسائل الواردة</h2>
                    <p>عرض وإدارة رسائل التواصل من العملاء</p>
                </div>

                <div class="admin-card">
                    <div class="messages-container" id="messages-container">
                        <div class="no-data">
                            <p>جاري تحميل الرسائل...</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Edit Branch Modal -->
    <div class="modal" id="edit-branch-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل الفرع</h3>
                <button class="modal-close" onclick="closeEditModal()">&times;</button>
            </div>
            <form id="edit-branch-form" class="admin-form">
                <input type="hidden" id="edit-branch-id">
                
                <div class="form-group">
                    <label for="edit-branch-name">اسم الفرع</label>
                    <input type="text" id="edit-branch-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="edit-branch-phone">رقم الهاتف</label>
                    <input type="tel" id="edit-branch-phone" name="phone">
                </div>
                
                <div class="form-group">
                    <label for="edit-branch-address">العنوان</label>
                    <textarea id="edit-branch-address" name="address" rows="3" required></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                    <button type="button" class="admin-btn secondary" onclick="closeEditModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Gallery Image Modal -->
    <div class="modal-overlay" id="edit-gallery-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل صورة المعرض</h3>
                <button class="close-btn" onclick="closeEditGalleryModal()">❌</button>
            </div>

            <form id="edit-gallery-form" class="admin-form">
                <input type="hidden" id="edit-image-id">

                <div class="form-group">
                    <label>الصورة الحالية</label>
                    <div class="current-image-preview">
                        <img id="edit-current-image" src="" alt="الصورة الحالية" style="max-width: 200px; max-height: 150px; border-radius: 8px;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit-image-file">تغيير الصورة (اختياري)</label>
                    <input type="file" id="edit-image-file" accept="image/*" onchange="previewEditImage(this)">
                    <small>اتركه فارغاً إذا كنت لا تريد تغيير الصورة</small>
                </div>

                <div class="form-group">
                    <label>معاينة الصورة الجديدة</label>
                    <div class="new-image-preview" id="new-image-preview" style="display: none;">
                        <img id="edit-new-image" src="" alt="الصورة الجديدة" style="max-width: 200px; max-height: 150px; border-radius: 8px;">
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit-image-title">عنوان الصورة</label>
                    <input type="text" id="edit-image-title" required>
                </div>

                <div class="form-group">
                    <label for="edit-image-description">وصف الصورة</label>
                    <textarea id="edit-image-description" rows="3" required></textarea>
                </div>

                <div class="form-group">
                    <label for="edit-image-category">فئة الصورة</label>
                    <select id="edit-image-category" required>
                        <option value="">اختر الفئة</option>
                        <option value="services">خدماتنا</option>
                        <option value="installation">التركيب</option>
                        <option value="repair">الإصلاح</option>
                        <option value="products">منتجاتنا</option>
                        <option value="general">عام</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="edit-image-featured">
                        <span class="checkmark"></span>
                        صورة مميزة
                    </label>
                </div>

                <div class="form-actions">
                    <button type="submit" class="admin-btn primary">💾 حفظ التغييرات</button>
                    <button type="button" class="admin-btn secondary" onclick="closeEditGalleryModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="admin-script.js"></script>
</body>
</html>
