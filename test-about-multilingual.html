<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قسم من نحن متعدد اللغات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .language-toggle {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .about-section {
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .about-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .about-description {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
        }
        .test-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        [dir="ltr"] {
            text-align: left;
        }
        [dir="rtl"] {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h2>اختبار قسم "من نحن" متعدد اللغات</h2>
            <p>هذه صفحة اختبار للتأكد من عمل النظام الجديد لإدارة محتوى "من نحن" باللغتين العربية والإنجليزية.</p>
        </div>

        <button class="language-toggle" onclick="toggleLanguage()" id="lang-btn">
            🌐 Switch to English
        </button>

        <div class="about-section">
            <h2 class="about-title" id="about-title" data-ar="من نحن" data-en="About Us">من نحن</h2>
            <div class="about-loading" id="about-loading" style="display: none;">
                <p>جاري تحميل المحتوى...</p>
            </div>
            <p class="about-description" id="about-description" 
               data-ar="نحن شركة السلامات لزجاج السيارات، رائدة في مجال تركيب وإصلاح زجاج السيارات بأعلى معايير الجودة والاحترافية." 
               data-en="We are AL-SALAMAT Car Glass, a leading company in car glass installation and repair with the highest standards of quality and professionalism.">
               نحن شركة السلامات لزجاج السيارات، رائدة في مجال تركيب وإصلاح زجاج السيارات بأعلى معايير الجودة والاحترافية.
            </p>
        </div>

        <div class="test-info">
            <h3>تعليمات الاختبار:</h3>
            <ol>
                <li>اضغط على زر تبديل اللغة لتجربة التبديل بين العربية والإنجليزية</li>
                <li>تأكد من تغيير اتجاه النص (RTL/LTR)</li>
                <li>تأكد من تحديث المحتوى بشكل صحيح</li>
                <li>افتح صفحة الإدارة وقم بتحديث محتوى "من نحن" لاختبار التحديث المباشر</li>
            </ol>
        </div>
    </div>

    <script>
        let currentLanguage = localStorage.getItem('language') || 'ar';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            localStorage.setItem('language', currentLanguage);
            updateLanguage();
        }

        function updateLanguage() {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            const langBtn = document.getElementById('lang-btn');

            elements.forEach(element => {
                if (currentLanguage === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });

            // Update language button
            if (langBtn) {
                langBtn.textContent = currentLanguage === 'ar' ? '🌐 Switch to English' : '🌐 التبديل للعربية';
            }

            // Update page direction
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = currentLanguage;
            document.documentElement.setAttribute('data-lang', currentLanguage);

            console.log(`Language switched to: ${currentLanguage}`);
        }

        // Initialize language on page load
        updateLanguage();

        // Simulate Firebase data update (for testing)
        function simulateDataUpdate() {
            const aboutTitle = document.getElementById('about-title');
            const aboutDescription = document.getElementById('about-description');

            // Simulate new data from Firebase
            const testData = {
                titleAr: 'من نحن - محدث',
                titleEn: 'About Us - Updated',
                descriptionAr: 'نحن شركة السلامات لزجاج السيارات، رائدة في مجال تركيب وإصلاح زجاج السيارات. نقدم خدمات عالية الجودة مع فريق من الخبراء المتخصصين.',
                descriptionEn: 'We are AL-SALAMAT Car Glass, a leading company in car glass installation and repair. We provide high-quality services with a team of specialized experts.'
            };

            // Update data attributes
            aboutTitle.setAttribute('data-ar', testData.titleAr);
            aboutTitle.setAttribute('data-en', testData.titleEn);
            aboutDescription.setAttribute('data-ar', testData.descriptionAr);
            aboutDescription.setAttribute('data-en', testData.descriptionEn);

            // Apply current language
            updateLanguage();

            console.log('Simulated data update applied');
        }

        // Add test button for data simulation
        setTimeout(() => {
            const testBtn = document.createElement('button');
            testBtn.textContent = 'محاكاة تحديث البيانات';
            testBtn.className = 'language-toggle';
            testBtn.onclick = simulateDataUpdate;
            testBtn.style.marginLeft = '10px';
            document.querySelector('.language-toggle').parentNode.appendChild(testBtn);
        }, 1000);
    </script>
</body>
</html>
