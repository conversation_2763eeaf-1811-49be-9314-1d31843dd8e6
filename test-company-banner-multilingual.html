<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار معلومات الشركة والشريط المتحرك متعدد اللغات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .language-toggle {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .test-section {
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .company-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .company-subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
        }
        .company-description {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
        }
        .moving-text-banner {
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 1rem 0;
            overflow: hidden;
            position: relative;
            margin: 20px 0;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            height: 60px;
            display: flex;
            align-items: center;
        }
        .moving-text {
            width: 100%;
            overflow: hidden;
        }
        .moving-text span {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
            letter-spacing: 1.2px;
            display: inline-block;
            min-width: max-content;
            position: relative;
            z-index: 3;
            animation: moveText 30s linear infinite;
        }
        @keyframes moveText {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        .test-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        [dir="ltr"] {
            text-align: left;
        }
        [dir="rtl"] {
            text-align: right;
        }
        .test-controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-controls button {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h2>اختبار معلومات الشركة والشريط المتحرك متعدد اللغات</h2>
            <p>هذه صفحة اختبار للتأكد من عمل النظام الجديد لإدارة معلومات الشركة والشريط المتحرك باللغتين العربية والإنجليزية.</p>
        </div>

        <button class="language-toggle" onclick="toggleLanguage()" id="lang-btn">
            🌐 Switch to English
        </button>

        <!-- Company Info Section -->
        <div class="test-section">
            <h3>معلومات الشركة</h3>
            <h1 class="company-title" id="company-title" 
                data-ar="السلامات لزجاج السيارات" 
                data-en="AL-SALAMAT Car Glass">
                السلامات لزجاج السيارات
            </h1>
            <h2 class="company-subtitle" id="company-subtitle" 
                data-ar="رائدة في زجاج السيارات" 
                data-en="Leading in Car Glass Services">
                رائدة في زجاج السيارات
            </h2>
            <p class="company-description" id="company-description" 
               data-ar="نحن شركة السلامات لزجاج السيارات، نقدم أفضل خدمات تركيب وإصلاح زجاج السيارات بأعلى معايير الجودة والاحترافية." 
               data-en="We are AL-SALAMAT Car Glass, providing the best car glass installation and repair services with the highest standards of quality and professionalism.">
               نحن شركة السلامات لزجاج السيارات، نقدم أفضل خدمات تركيب وإصلاح زجاج السيارات بأعلى معايير الجودة والاحترافية.
            </p>
        </div>

        <!-- Banner Section -->
        <div class="test-section">
            <h3>الشريط المتحرك</h3>
            <div class="moving-text-banner">
                <div class="moving-text">
                    <span id="banner-text" 
                          data-ar="السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات" 
                          data-en="AL-SALAMAT Car Glass - Specialists in car glass replacement and installation">
                          السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات
                    </span>
                </div>
            </div>
        </div>

        <div class="test-controls">
            <h3>أدوات الاختبار:</h3>
            <button onclick="simulateCompanyUpdate()">محاكاة تحديث معلومات الشركة</button>
            <button onclick="simulateBannerUpdate()">محاكاة تحديث الشريط المتحرك</button>
            <button onclick="resetToDefaults()">إعادة تعيين القيم الافتراضية</button>
        </div>

        <div class="test-info">
            <h3>تعليمات الاختبار:</h3>
            <ol>
                <li>اضغط على زر تبديل اللغة لتجربة التبديل بين العربية والإنجليزية</li>
                <li>تأكد من تغيير اتجاه النص (RTL/LTR)</li>
                <li>تأكد من تحديث جميع النصوص بشكل صحيح</li>
                <li>جرب أزرار المحاكاة لاختبار التحديث الديناميكي</li>
                <li>افتح صفحة الإدارة وقم بتحديث المحتوى لاختبار التحديث المباشر</li>
            </ol>
        </div>
    </div>

    <script>
        let currentLanguage = localStorage.getItem('language') || 'ar';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            localStorage.setItem('language', currentLanguage);
            updateLanguage();
        }

        function updateLanguage() {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            const langBtn = document.getElementById('lang-btn');

            elements.forEach(element => {
                if (currentLanguage === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });

            // Update language button
            if (langBtn) {
                langBtn.textContent = currentLanguage === 'ar' ? '🌐 Switch to English' : '🌐 التبديل للعربية';
            }

            // Update page direction
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
            document.documentElement.lang = currentLanguage;
            document.documentElement.setAttribute('data-lang', currentLanguage);

            console.log(`Language switched to: ${currentLanguage}`);
        }

        // Simulate company info update
        function simulateCompanyUpdate() {
            const companyTitle = document.getElementById('company-title');
            const companySubtitle = document.getElementById('company-subtitle');
            const companyDescription = document.getElementById('company-description');

            const testData = {
                titleAr: 'السلامات لزجاج السيارات - محدث',
                titleEn: 'AL-SALAMAT Car Glass - Updated',
                subtitleAr: 'رائدة في زجاج السيارات - جديد',
                subtitleEn: 'Leading in Car Glass Services - New',
                descriptionAr: 'نحن شركة السلامات لزجاج السيارات المحدثة، نقدم أفضل خدمات تركيب وإصلاح زجاج السيارات مع التطوير المستمر.',
                descriptionEn: 'We are AL-SALAMAT Car Glass Updated, providing the best car glass installation and repair services with continuous development.'
            };

            // Update data attributes
            companyTitle.setAttribute('data-ar', testData.titleAr);
            companyTitle.setAttribute('data-en', testData.titleEn);
            companySubtitle.setAttribute('data-ar', testData.subtitleAr);
            companySubtitle.setAttribute('data-en', testData.subtitleEn);
            companyDescription.setAttribute('data-ar', testData.descriptionAr);
            companyDescription.setAttribute('data-en', testData.descriptionEn);

            updateLanguage();
            console.log('Company info updated');
        }

        // Simulate banner update
        function simulateBannerUpdate() {
            const bannerText = document.getElementById('banner-text');

            const testData = {
                textAr: 'السلامات لزجاج السيارات المحدثة - خدمات متطورة ومتخصصة',
                textEn: 'AL-SALAMAT Car Glass Updated - Advanced and specialized services'
            };

            bannerText.setAttribute('data-ar', testData.textAr);
            bannerText.setAttribute('data-en', testData.textEn);

            updateLanguage();
            console.log('Banner updated');
        }

        // Reset to defaults
        function resetToDefaults() {
            location.reload();
        }

        // Initialize language on page load
        updateLanguage();
    </script>
</body>
</html>
