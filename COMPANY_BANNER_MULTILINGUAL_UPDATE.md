# تحديث معلومات الشركة والشريط المتحرك متعدد اللغات - شركة السلامات لزجاج السيارات

## نظرة عامة
تم تحديث نظام إدارة معلومات الشركة والشريط المتحرك ليدعم اللغتين العربية والإنجليزية بشكل كامل، مما يتيح للمدير إدارة المحتوى بكلا اللغتين من صفحة الإدارة.

## المميزات الجديدة

### 1. إدارة معلومات الشركة متعددة اللغات
- **العنوان الرئيسي**: حقول منفصلة للعنوان الرئيسي باللغتين العربية والإنجليزية
- **العنوان الفرعي**: حقول منفصلة للعنوان الفرعي باللغتين
- **وصف الشركة**: مناطق نص منفصلة للوصف باللغتين
- **واجهة محسنة**: تصميم منظم يعرض الحقول جنباً إلى جنب

### 2. إدارة الشريط المتحرك متعدد اللغات
- **نص الشريط**: حقول منفصلة لنص الشريط المتحرك باللغتين
- **معاينة محسنة**: معاينة تعرض النص بكلا اللغتين
- **تحكم في التفعيل**: إمكانية تفعيل/إلغاء تفعيل الشريط

### 3. عرض ديناميكي حسب اللغة
- **تبديل فوري**: يتغير المحتوى فوراً عند تبديل اللغة
- **حفظ التفضيلات**: يحفظ النظام اللغة المختارة
- **دعم RTL/LTR**: تغيير اتجاه النص تلقائياً

## الملفات المحدثة

### 1. admin.html
#### معلومات الشركة:
```html
<div class="translation-group">
    <h4>العنوان الرئيسي</h4>
    <div class="form-row">
        <div class="form-group">
            <label for="company-title-ar">العربية</label>
            <input type="text" id="company-title-ar" name="titleAr" placeholder="السلامات لزجاج السيارات" required>
        </div>
        <div class="form-group">
            <label for="company-title-en">English</label>
            <input type="text" id="company-title-en" name="titleEn" placeholder="AL-SALAMAT Car Glass" required>
        </div>
    </div>
</div>
```

#### الشريط المتحرك:
```html
<div class="translation-group">
    <h4>نص الشريط المتحرك</h4>
    <div class="form-row">
        <div class="form-group">
            <label for="banner-text-ar">العربية</label>
            <textarea id="banner-text-ar" name="bannerTextAr" rows="3" required></textarea>
        </div>
        <div class="form-group">
            <label for="banner-text-en">English</label>
            <textarea id="banner-text-en" name="bannerTextEn" rows="3" required></textarea>
        </div>
    </div>
</div>
```

### 2. admin-script.js
#### معلومات الشركة:
- **loadCompanyInfo()**: محدثة لتحميل البيانات بكلا اللغتين
- **saveCompanyInfo()**: محدثة لحفظ البيانات بكلا اللغتين مع التوافق العكسي

#### الشريط المتحرك:
- **loadBannerSettings()**: محدثة لتحميل نصوص الشريط بكلا اللغتين
- **saveBannerSettings()**: محدثة لحفظ النصوص بكلا اللغتين
- **previewBanner()**: محدثة لعرض معاينة بكلا اللغتين

### 3. dynamic-content.js
- **updateCompanyInfo()**: محدثة لعرض معلومات الشركة حسب اللغة
- **updateBannerSettings()**: محدثة لعرض الشريط المتحرك حسب اللغة

## بنية البيانات الجديدة

### معلومات الشركة (siteContent)
```json
{
  "siteContent": {
    "titleAr": "السلامات لزجاج السيارات",
    "titleEn": "AL-SALAMAT Car Glass",
    "subtitleAr": "رائدة في زجاج السيارات",
    "subtitleEn": "Leading in Car Glass Services",
    "descriptionAr": "وصف الشركة باللغة العربية",
    "descriptionEn": "Company description in English",
    "title": "السلامات لزجاج السيارات",  // للتوافق العكسي
    "subtitle": "رائدة في زجاج السيارات",  // للتوافق العكسي
    "description": "وصف الشركة باللغة العربية",  // للتوافق العكسي
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### الشريط المتحرك (bannerSettings)
```json
{
  "bannerSettings": {
    "textAr": "السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات",
    "textEn": "AL-SALAMAT Car Glass - Specialists in car glass replacement and installation",
    "enabled": true,
    "text": "السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات",  // للتوافق العكسي
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## كيفية الاستخدام

### للمدير:
1. **معلومات الشركة**:
   - افتح صفحة الإدارة → "🏢 معلومات الشركة"
   - أدخل العنوان الرئيسي والفرعي والوصف باللغتين
   - اضغط "💾 حفظ التغييرات"

2. **الشريط المتحرك**:
   - افتح صفحة الإدارة → "📢 الشريط المتحرك"
   - أدخل نص الشريط باللغتين
   - استخدم "👁️ معاينة" لرؤية النتيجة
   - اضغط "💾 حفظ التغييرات"

### للزائر:
- اضغط على زر 🌐 في الهيدر لتبديل اللغة
- سيتغير محتوى معلومات الشركة والشريط المتحرك تلقائياً

## الاختبار

### ملف الاختبار
تم إنشاء ملف `test-company-banner-multilingual.html` لاختبار النظام:
- اختبار تبديل اللغات
- اختبار عرض معلومات الشركة
- اختبار الشريط المتحرك
- محاكاة تحديث البيانات

### خطوات الاختبار
1. افتح `test-company-banner-multilingual.html`
2. جرب تبديل اللغة
3. تأكد من تغيير جميع النصوص
4. جرب أزرار المحاكاة
5. افتح صفحة الإدارة وحدث المحتوى

## المميزات التقنية

### 1. التوافق العكسي
- يدعم البيانات القديمة المحفوظة بالعربية فقط
- يحول البيانات القديمة للنظام الجديد تلقائياً
- لا يكسر أي وظائف موجودة

### 2. الأداء
- تحميل ذكي للبيانات
- تحديث فوري بدون إعادة تحميل
- ذاكرة تخزين محلية للغة

### 3. سهولة الاستخدام
- واجهة بديهية ومنظمة
- معاينة فورية للتغييرات
- رسائل تأكيد واضحة

## استكشاف الأخطاء

### مشاكل شائعة:
1. **النص لا يتغير عند تبديل اللغة**:
   - تأكد من وجود البيانات بكلا اللغتين
   - تحقق من console.log للأخطاء

2. **الشريط المتحرك لا يظهر**:
   - تأكد من تفعيل الشريط في الإعدادات
   - تحقق من وجود نص بإحدى اللغتين

3. **البيانات لا تحفظ**:
   - تأكد من اتصال Firebase
   - تحقق من صلاحيات قاعدة البيانات

## التطوير المستقبلي

### إضافة لغات جديدة:
1. أضف حقول جديدة في admin.html
2. حدث وظائف الحفظ والتحميل
3. أضف دعم اللغة في updateLanguage()
4. حدث قاعدة البيانات

### تحسينات مقترحة:
- إضافة محرر نصوص غني
- دعم الصور متعددة اللغات
- نظام ترجمة تلقائية
- إحصائيات استخدام اللغات

## الخلاصة
تم تحديث نظام إدارة معلومات الشركة والشريط المتحرك بنجاح ليدعم اللغتين العربية والإنجليزية مع الحفاظ على جميع الوظائف الحالية وإضافة مميزات جديدة لتحسين تجربة المستخدم والمدير.
